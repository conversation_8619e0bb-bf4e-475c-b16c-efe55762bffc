import 'package:hive/hive.dart';

part 'stream.g.dart';

@HiveType(typeId: 9)
class LiveStream extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String hostId;

  @HiveField(2)
  String title;

  @HiveField(3)
  String? description;

  @HiveField(4)
  String? thumbnailUrl;

  @HiveField(5)
  StreamStatus status;

  @HiveField(6)
  StreamQuality quality;

  @HiveField(7)
  DateTime createdAt;

  @HiveField(8)
  DateTime? startedAt;

  @HiveField(9)
  DateTime? endedAt;

  @HiveField(10)
  int viewerCount;

  @HiveField(11)
  int maxViewers;

  @HiveField(12)
  int? duration; // in seconds

  @HiveField(13)
  List<String> tags;

  @HiveField(14)
  bool isPrivate;

  @HiveField(15)
  String? password;

  @HiveField(16)
  Map<String, dynamic>? metadata;

  @HiveField(17)
  bool allowComments;

  @HiveField(18)
  bool allowReactions;

  @HiveField(19)
  String? category;

  LiveStream({
    required this.id,
    required this.hostId,
    required this.title,
    this.description,
    this.thumbnailUrl,
    this.status = StreamStatus.scheduled,
    this.quality = StreamQuality.hd,
    DateTime? createdAt,
    this.startedAt,
    this.endedAt,
    this.viewerCount = 0,
    this.maxViewers = 0,
    this.duration,
    this.tags = const [],
    this.isPrivate = false,
    this.password,
    this.metadata,
    this.allowComments = true,
    this.allowReactions = true,
    this.category,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Create a copy of the stream with updated fields
  LiveStream copyWith({
    String? id,
    String? hostId,
    String? title,
    String? description,
    String? thumbnailUrl,
    StreamStatus? status,
    StreamQuality? quality,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? endedAt,
    int? viewerCount,
    int? maxViewers,
    int? duration,
    List<String>? tags,
    bool? isPrivate,
    String? password,
    Map<String, dynamic>? metadata,
    bool? allowComments,
    bool? allowReactions,
    String? category,
  }) {
    return LiveStream(
      id: id ?? this.id,
      hostId: hostId ?? this.hostId,
      title: title ?? this.title,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      status: status ?? this.status,
      quality: quality ?? this.quality,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      endedAt: endedAt ?? this.endedAt,
      viewerCount: viewerCount ?? this.viewerCount,
      maxViewers: maxViewers ?? this.maxViewers,
      duration: duration ?? this.duration,
      tags: tags ?? this.tags,
      isPrivate: isPrivate ?? this.isPrivate,
      password: password ?? this.password,
      metadata: metadata ?? this.metadata,
      allowComments: allowComments ?? this.allowComments,
      allowReactions: allowReactions ?? this.allowReactions,
      category: category ?? this.category,
    );
  }

  /// Convert stream to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hostId': hostId,
      'title': title,
      'description': description,
      'thumbnailUrl': thumbnailUrl,
      'status': status.name,
      'quality': quality.name,
      'createdAt': createdAt.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'endedAt': endedAt?.toIso8601String(),
      'viewerCount': viewerCount,
      'maxViewers': maxViewers,
      'duration': duration,
      'tags': tags,
      'isPrivate': isPrivate,
      'password': password,
      'metadata': metadata,
      'allowComments': allowComments,
      'allowReactions': allowReactions,
      'category': category,
    };
  }

  /// Create stream from JSON
  factory LiveStream.fromJson(Map<String, dynamic> json) {
    return LiveStream(
      id: json['id'] as String,
      hostId: json['hostId'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      status: StreamStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => StreamStatus.scheduled,
      ),
      quality: StreamQuality.values.firstWhere(
        (e) => e.name == json['quality'],
        orElse: () => StreamQuality.hd,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      startedAt: json['startedAt'] != null
          ? DateTime.parse(json['startedAt'] as String)
          : null,
      endedAt: json['endedAt'] != null
          ? DateTime.parse(json['endedAt'] as String)
          : null,
      viewerCount: json['viewerCount'] as int? ?? 0,
      maxViewers: json['maxViewers'] as int? ?? 0,
      duration: json['duration'] as int?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      isPrivate: json['isPrivate'] as bool? ?? false,
      password: json['password'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      allowComments: json['allowComments'] as bool? ?? true,
      allowReactions: json['allowReactions'] as bool? ?? true,
      category: json['category'] as String?,
    );
  }

  /// Check if stream is live
  bool get isLive => status == StreamStatus.live;

  /// Check if stream is ended
  bool get isEnded => status == StreamStatus.ended;

  /// Check if stream is scheduled
  bool get isScheduled => status == StreamStatus.scheduled;

  /// Get stream status text
  String get statusText {
    switch (status) {
      case StreamStatus.scheduled:
        return 'Scheduled';
      case StreamStatus.live:
        return 'Live';
      case StreamStatus.ended:
        return 'Ended';
      case StreamStatus.paused:
        return 'Paused';
    }
  }

  /// Get stream quality text
  String get qualityText {
    switch (quality) {
      case StreamQuality.low:
        return '480p';
      case StreamQuality.medium:
        return '720p';
      case StreamQuality.hd:
        return '1080p';
      case StreamQuality.fullHd:
        return '1440p';
      case StreamQuality.uhd:
        return '4K';
    }
  }

  /// Get viewer count text
  String get viewerCountText {
    if (viewerCount < 1000) {
      return viewerCount.toString();
    } else if (viewerCount < 1000000) {
      return '${(viewerCount / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(viewerCount / 1000000).toStringAsFixed(1)}M';
    }
  }

  /// Get duration text
  String get durationText {
    if (duration == null) return '00:00';
    
    final hours = duration! ~/ 3600;
    final minutes = (duration! % 3600) ~/ 60;
    final seconds = duration! % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Get stream URL (placeholder - would be generated by streaming service)
  String get streamUrl => 'rtmp://stream.example.com/live/$id';

  /// Get playback URL (placeholder - would be generated by streaming service)
  String get playbackUrl => 'https://stream.example.com/hls/$id/playlist.m3u8';

  @override
  String toString() {
    return 'LiveStream(id: $id, title: $title, status: $status, viewers: $viewerCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LiveStream && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@HiveType(typeId: 10)
enum StreamStatus {
  @HiveField(0)
  scheduled,
  @HiveField(1)
  live,
  @HiveField(2)
  ended,
  @HiveField(3)
  paused,
}

@HiveType(typeId: 11)
enum StreamQuality {
  @HiveField(0)
  low,      // 480p
  @HiveField(1)
  medium,   // 720p
  @HiveField(2)
  hd,       // 1080p
  @HiveField(3)
  fullHd,   // 1440p
  @HiveField(4)
  uhd,      // 4K
}
