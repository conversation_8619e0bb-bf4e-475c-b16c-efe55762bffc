# ZEGOCloud Configuration Guide

This guide provides detailed instructions for configuring ZEGOCloud services in the CMT Connect application.

## Prerequisites

### ZEGOCloud Account Setup

1. **Create Account**
   - Visit [ZEGOCloud Console](https://console.zegocloud.com/)
   - Sign up for a free account
   - Verify your email address

2. **Create Project**
   - Click "Create Project" in the console
   - Choose "Real-time Communication" as the project type
   - Enter project name: "CMT Connect PoC"
   - Select your region

3. **Get Credentials**
   - Navigate to your project dashboard
   - Copy the **App ID** (numeric value)
   - Copy the **App Sign** (string value)

## Configuration Steps

### 1. Update App Configuration

Edit `lib/config/app_config.dart`:

```dart
class AppConfig {
  // Replace with your actual ZEGOCloud credentials
  static const int zegoAppID = *********; // Your App ID here
  static const String zegoAppSign = 'your_app_sign_here'; // Your App Sign here
  
  // ... rest of configuration remains the same
}
```

### 2. Enable Developer Mode (Windows)

For Windows development, enable Developer Mode:

1. Open Settings (Windows + I)
2. Go to Update & Security > For developers
3. Enable "Developer Mode"
4. Restart your computer if prompted

### 3. Platform-Specific Setup

#### Android Configuration

The following files are already configured:

**android/app/build.gradle**:
- Minimum SDK: 21
- Target SDK: Latest
- Multidex enabled

**android/app/src/main/AndroidManifest.xml**:
- Camera permission
- Microphone permission
- Internet permission
- Network state permission
- Bluetooth permission
- Audio settings permission
- External storage permission
- Phone state permission
- Wake lock permission
- Notification permission (Android 13+)
- Foreground service permission

#### iOS Configuration

**ios/Runner/Info.plist**:
- Camera usage description
- Microphone usage description
- Background modes (audio, voip)
- App Transport Security settings

### 4. Firebase Setup (Optional)

For push notifications:

#### Android Firebase Setup

1. Create Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Add Android app with package name: `com.example.cmtconnect.cmt_connect_zegocloud_poc`
3. Download `google-services.json`
4. Place in `android/app/` directory

#### iOS Firebase Setup

1. Add iOS app to Firebase project
2. Download `GoogleService-Info.plist`
3. Add to iOS project in Xcode
4. Ensure it's added to the target

## ZEGOCloud Service Configuration

### 1. Call Service

The app uses ZEGOCloud's Call Kit for:
- 1-on-1 voice calls
- 1-on-1 video calls
- Group video conferences
- Call invitation system
- Background call handling

### 2. Chat Service

Uses ZIM (ZEGOCloud Instant Messaging) for:
- Real-time messaging
- Message delivery status
- Typing indicators
- Chat history
- Offline message sync

### 3. Live Streaming

Uses ZEGOCloud Live Streaming for:
- Host streaming capabilities
- Audience viewing
- Interactive features
- Stream quality management
- Viewer count tracking

## Testing Configuration

### 1. Verify Setup

Run the application and check:

1. **No Configuration Warnings**: App should not show ZEGOCloud configuration warnings
2. **Authentication Works**: Test users should be able to log in
3. **Services Initialize**: Check debug logs for successful service initialization

### 2. Test Features

#### Demo Mode Testing

If ZEGOCloud is not configured, the app runs in demo mode:
- Authentication works with test users
- Call simulation (30-second demo calls)
- Chat simulation (local messages only)
- Streaming simulation (demo streams)

#### Full Feature Testing

With proper ZEGOCloud configuration:
- Real voice/video calls between users
- Actual message delivery
- Live streaming functionality
- Push notifications

## Troubleshooting

### Common Issues

#### 1. Configuration Warnings

**Problem**: App shows "Configuration Required" screen

**Solution**:
- Verify App ID and App Sign are correct
- Check for typos in configuration
- Ensure ZEGOCloud account is active

#### 2. Build Errors

**Problem**: Symlink support errors on Windows

**Solution**:
- Enable Developer Mode in Windows settings
- Run `flutter clean && flutter pub get`
- Restart IDE

#### 3. Permission Issues

**Problem**: Camera/microphone not working

**Solution**:
- Check device permissions in system settings
- Test on physical device (not simulator)
- Verify platform-specific permission configurations

#### 4. Network Issues

**Problem**: Services fail to connect

**Solution**:
- Check internet connectivity
- Verify firewall settings
- Test with different network

### Debug Mode

Enable detailed logging in `lib/config/app_config.dart`:

```dart
static const bool enableLogging = true;
static const bool enableDebugMode = true;
```

### Production Considerations

Before production deployment:

1. **Security**
   - Store credentials securely (not in source code)
   - Implement proper authentication
   - Use environment variables

2. **Performance**
   - Test with multiple concurrent users
   - Monitor memory usage
   - Optimize for battery consumption

3. **Compliance**
   - Implement privacy policies
   - Handle data protection requirements
   - Ensure regional compliance

## Support Resources

- [ZEGOCloud Documentation](https://docs.zegocloud.com/)
- [Flutter Integration Guide](https://docs.zegocloud.com/article/14826)
- [ZEGOCloud Community](https://discord.gg/EtH4gmae)
- [Technical Support](https://console.zegocloud.com/support)

## Next Steps

1. Configure ZEGOCloud credentials
2. Test basic functionality
3. Implement additional features as needed
4. Deploy to test environment
5. Conduct user acceptance testing

---

**Note**: This is a PoC application. For production use, implement additional security measures, error handling, and testing.
