import 'dart:async';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:zego_zim/zego_zim.dart';
import '../models/message.dart';
import '../models/user.dart';
import '../config/app_config.dart';
import 'authentication_service.dart';

/// Service for handling real-time messaging (Demo Implementation)
class ChatService {
  static final ChatService _instance = ChatService._internal();
  factory ChatService() => _instance;
  ChatService._internal();

  final StreamController<Message> _messageController = StreamController<Message>.broadcast();
  final StreamController<List<Message>> _conversationController = StreamController<List<Message>>.broadcast();
  final StreamController<Map<String, bool>> _typingController = StreamController<Map<String, bool>>.broadcast();

  final Map<String, List<Message>> _conversations = {};
  final Map<String, bool> _typingUsers = {};

  /// Stream of new messages
  Stream<Message> get messageStream => _messageController.stream;

  /// Stream of conversation updates
  Stream<List<Message>> get conversationStream => _conversationController.stream;

  /// Stream of typing indicators
  Stream<Map<String, bool>> get typingStream => _typingController.stream;

  /// Initialize the chat service
  Future<void> initialize() async {
    // TODO: Initialize ZIM SDK when properly configured
  }

  /// Login to chat service
  Future<void> login() async {
    // TODO: Implement ZIM login when properly configured
  }

  /// Logout from chat service
  Future<void> logout() async {
    _conversations.clear();
    _typingUsers.clear();
  }

  /// Send a text message (Demo Implementation)
  Future<Message> sendTextMessage({
    required String receiverId,
    required String content,
  }) async {
    final currentUser = AuthenticationService().currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final messageId = const Uuid().v4();
    final message = Message(
      id: messageId,
      senderId: currentUser.id,
      conversationId: _getConversationId(currentUser.id, receiverId),
      content: content,
      type: MessageType.text,
      status: MessageStatus.sending,
    );

    // Add to local conversation
    _addMessageToConversation(message);

    // Demo mode - simulate sending
    await Future.delayed(const Duration(milliseconds: 500));
    final sentMessage = message.copyWith(status: MessageStatus.sent);
    _updateMessageInConversation(sentMessage);
    return sentMessage;
  }

  /// Get conversation messages
  List<Message> getConversationMessages(String conversationId) {
    return _conversations[conversationId] ?? [];
  }

  /// Get all conversations
  Map<String, List<Message>> getAllConversations() {
    return Map.unmodifiable(_conversations);
  }

  /// Start typing indicator
  Future<void> startTyping(String receiverId) async {
    // TODO: Implement typing indicator using ZIM custom commands
    _typingUsers[receiverId] = true;
    _typingController.add(Map.from(_typingUsers));
  }

  /// Stop typing indicator
  Future<void> stopTyping(String receiverId) async {
    // TODO: Implement typing indicator using ZIM custom commands
    _typingUsers.remove(receiverId);
    _typingController.add(Map.from(_typingUsers));
  }



  /// Add message to conversation
  void _addMessageToConversation(Message message) {
    final conversationId = message.conversationId;
    _conversations[conversationId] ??= [];
    _conversations[conversationId]!.add(message);
    _conversations[conversationId]!.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    _conversationController.add(List.from(_conversations[conversationId]!));
  }

  /// Update message in conversation
  void _updateMessageInConversation(Message message) {
    final conversationId = message.conversationId;
    final conversation = _conversations[conversationId];
    if (conversation != null) {
      final index = conversation.indexWhere((m) => m.id == message.id);
      if (index != -1) {
        conversation[index] = message;
        _conversationController.add(List.from(conversation));
      }
    }
  }

  /// Generate conversation ID from two user IDs
  String _getConversationId(String userId1, String userId2) {
    final sortedIds = [userId1, userId2]..sort();
    return '${sortedIds[0]}_${sortedIds[1]}';
  }

  /// Dispose the service
  void dispose() {
    _messageController.close();
    _conversationController.close();
    _typingController.close();
  }
}
