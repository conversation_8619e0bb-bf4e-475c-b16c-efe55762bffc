// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CallAdapter extends TypeAdapter<Call> {
  @override
  final int typeId = 2;

  @override
  Call read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Call(
      id: fields[0] as String,
      callerId: fields[1] as String,
      participantIds: (fields[2] as List).cast<String>(),
      type: fields[3] as CallType,
      status: fields[4] as CallStatus,
      createdAt: fields[5] as DateTime?,
      startedAt: fields[6] as DateTime?,
      endedAt: fields[7] as DateTime?,
      duration: fields[8] as int?,
      roomId: fields[9] as String?,
      isVideoEnabled: fields[10] as bool,
      isAudioEnabled: fields[11] as bool,
      endReason: fields[12] as CallEndReason?,
      metadata: (fields[13] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, Call obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.callerId)
      ..writeByte(2)
      ..write(obj.participantIds)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.startedAt)
      ..writeByte(7)
      ..write(obj.endedAt)
      ..writeByte(8)
      ..write(obj.duration)
      ..writeByte(9)
      ..write(obj.roomId)
      ..writeByte(10)
      ..write(obj.isVideoEnabled)
      ..writeByte(11)
      ..write(obj.isAudioEnabled)
      ..writeByte(12)
      ..write(obj.endReason)
      ..writeByte(13)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CallTypeAdapter extends TypeAdapter<CallType> {
  @override
  final int typeId = 3;

  @override
  CallType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CallType.voice;
      case 1:
        return CallType.video;
      case 2:
        return CallType.conference;
      default:
        return CallType.voice;
    }
  }

  @override
  void write(BinaryWriter writer, CallType obj) {
    switch (obj) {
      case CallType.voice:
        writer.writeByte(0);
        break;
      case CallType.video:
        writer.writeByte(1);
        break;
      case CallType.conference:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CallStatusAdapter extends TypeAdapter<CallStatus> {
  @override
  final int typeId = 4;

  @override
  CallStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CallStatus.pending;
      case 1:
        return CallStatus.ringing;
      case 2:
        return CallStatus.connected;
      case 3:
        return CallStatus.ended;
      case 4:
        return CallStatus.missed;
      case 5:
        return CallStatus.declined;
      case 6:
        return CallStatus.timeout;
      case 7:
        return CallStatus.failed;
      default:
        return CallStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, CallStatus obj) {
    switch (obj) {
      case CallStatus.pending:
        writer.writeByte(0);
        break;
      case CallStatus.ringing:
        writer.writeByte(1);
        break;
      case CallStatus.connected:
        writer.writeByte(2);
        break;
      case CallStatus.ended:
        writer.writeByte(3);
        break;
      case CallStatus.missed:
        writer.writeByte(4);
        break;
      case CallStatus.declined:
        writer.writeByte(5);
        break;
      case CallStatus.timeout:
        writer.writeByte(6);
        break;
      case CallStatus.failed:
        writer.writeByte(7);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CallEndReasonAdapter extends TypeAdapter<CallEndReason> {
  @override
  final int typeId = 5;

  @override
  CallEndReason read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CallEndReason.hangup;
      case 1:
        return CallEndReason.timeout;
      case 2:
        return CallEndReason.declined;
      case 3:
        return CallEndReason.networkError;
      case 4:
        return CallEndReason.unknown;
      default:
        return CallEndReason.hangup;
    }
  }

  @override
  void write(BinaryWriter writer, CallEndReason obj) {
    switch (obj) {
      case CallEndReason.hangup:
        writer.writeByte(0);
        break;
      case CallEndReason.timeout:
        writer.writeByte(1);
        break;
      case CallEndReason.declined:
        writer.writeByte(2);
        break;
      case CallEndReason.networkError:
        writer.writeByte(3);
        break;
      case CallEndReason.unknown:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallEndReasonAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
