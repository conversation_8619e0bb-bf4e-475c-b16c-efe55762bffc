import 'package:hive/hive.dart';

part 'message.g.dart';

@HiveType(typeId: 6)
class Message extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String senderId;

  @HiveField(2)
  String conversationId;

  @HiveField(3)
  String content;

  @HiveField(4)
  MessageType type;

  @HiveField(5)
  MessageStatus status;

  @HiveField(6)
  DateTime createdAt;

  @HiveField(7)
  DateTime? updatedAt;

  @HiveField(8)
  DateTime? deliveredAt;

  @HiveField(9)
  DateTime? readAt;

  @HiveField(10)
  String? replyToMessageId;

  @HiveField(11)
  Map<String, dynamic>? metadata;

  @HiveField(12)
  List<String>? attachments;

  @HiveField(13)
  bool isEdited;

  @HiveField(14)
  bool isDeleted;

  Message({
    required this.id,
    required this.senderId,
    required this.conversationId,
    required this.content,
    this.type = MessageType.text,
    this.status = MessageStatus.sending,
    DateTime? createdAt,
    this.updatedAt,
    this.deliveredAt,
    this.readAt,
    this.replyToMessageId,
    this.metadata,
    this.attachments,
    this.isEdited = false,
    this.isDeleted = false,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Create a copy of the message with updated fields
  Message copyWith({
    String? id,
    String? senderId,
    String? conversationId,
    String? content,
    MessageType? type,
    MessageStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deliveredAt,
    DateTime? readAt,
    String? replyToMessageId,
    Map<String, dynamic>? metadata,
    List<String>? attachments,
    bool? isEdited,
    bool? isDeleted,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      conversationId: conversationId ?? this.conversationId,
      content: content ?? this.content,
      type: type ?? this.type,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      readAt: readAt ?? this.readAt,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      metadata: metadata ?? this.metadata,
      attachments: attachments ?? this.attachments,
      isEdited: isEdited ?? this.isEdited,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// Convert message to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'conversationId': conversationId,
      'content': content,
      'type': type.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'deliveredAt': deliveredAt?.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
      'replyToMessageId': replyToMessageId,
      'metadata': metadata,
      'attachments': attachments,
      'isEdited': isEdited,
      'isDeleted': isDeleted,
    };
  }

  /// Create message from JSON
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as String,
      senderId: json['senderId'] as String,
      conversationId: json['conversationId'] as String,
      content: json['content'] as String,
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.text,
      ),
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      deliveredAt: json['deliveredAt'] != null
          ? DateTime.parse(json['deliveredAt'] as String)
          : null,
      readAt: json['readAt'] != null
          ? DateTime.parse(json['readAt'] as String)
          : null,
      replyToMessageId: json['replyToMessageId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      attachments: json['attachments'] != null
          ? List<String>.from(json['attachments'] as List)
          : null,
      isEdited: json['isEdited'] as bool? ?? false,
      isDeleted: json['isDeleted'] as bool? ?? false,
    );
  }

  /// Check if message is sent by current user
  bool isSentByMe(String currentUserId) => senderId == currentUserId;

  /// Check if message is read
  bool get isRead => readAt != null;

  /// Check if message is delivered
  bool get isDelivered => deliveredAt != null;

  /// Check if message has attachments
  bool get hasAttachments => attachments != null && attachments!.isNotEmpty;

  /// Check if message is a reply
  bool get isReply => replyToMessageId != null;

  /// Get message status text
  String get statusText {
    switch (status) {
      case MessageStatus.sending:
        return 'Sending...';
      case MessageStatus.sent:
        return 'Sent';
      case MessageStatus.delivered:
        return 'Delivered';
      case MessageStatus.read:
        return 'Read';
      case MessageStatus.failed:
        return 'Failed';
    }
  }

  /// Get message type text
  String get typeText {
    switch (type) {
      case MessageType.text:
        return 'Text';
      case MessageType.image:
        return 'Image';
      case MessageType.video:
        return 'Video';
      case MessageType.audio:
        return 'Audio';
      case MessageType.file:
        return 'File';
      case MessageType.location:
        return 'Location';
      case MessageType.system:
        return 'System';
    }
  }

  /// Get display content based on message type
  String get displayContent {
    if (isDeleted) return 'This message was deleted';
    
    switch (type) {
      case MessageType.text:
        return content;
      case MessageType.image:
        return '📷 Image';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.file:
        return '📎 File';
      case MessageType.location:
        return '📍 Location';
      case MessageType.system:
        return content;
    }
  }

  /// Get time display text
  String get timeText {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${createdAt.day}/${createdAt.month}';
    }
  }

  @override
  String toString() {
    return 'Message(id: $id, type: $type, status: $status, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@HiveType(typeId: 7)
enum MessageType {
  @HiveField(0)
  text,
  @HiveField(1)
  image,
  @HiveField(2)
  video,
  @HiveField(3)
  audio,
  @HiveField(4)
  file,
  @HiveField(5)
  location,
  @HiveField(6)
  system,
}

@HiveType(typeId: 8)
enum MessageStatus {
  @HiveField(0)
  sending,
  @HiveField(1)
  sent,
  @HiveField(2)
  delivered,
  @HiveField(3)
  read,
  @HiveField(4)
  failed,
}
