import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:permission_handler/permission_handler.dart';

import 'config/app_config.dart';
import 'config/zego_config.dart';
import 'models/user.dart';
import 'models/call.dart';
import 'models/message.dart';
import 'models/stream.dart';
import 'services/authentication_service.dart';
import 'services/call_service.dart';
import 'app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    await Firebase.initializeApp();
  } catch (e) {
    debugPrint('Firebase initialization failed: $e');
    // Continue without Firebase for demo purposes
  }

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(UserStatusAdapter());
  Hive.registerAdapter(CallAdapter());
  Hive.registerAdapter(CallTypeAdapter());
  Hive.registerAdapter(CallStatusAdapter());
  Hive.registerAdapter(CallEndReasonAdapter());
  Hive.registerAdapter(MessageAdapter());
  Hive.registerAdapter(MessageTypeAdapter());
  Hive.registerAdapter(MessageStatusAdapter());
  Hive.registerAdapter(LiveStreamAdapter());
  Hive.registerAdapter(StreamStatusAdapter());
  Hive.registerAdapter(StreamQualityAdapter());

  // Request permissions
  await _requestPermissions();

  // Validate configuration
  if (!AppConfig.isConfigValid) {
    debugPrint('⚠️ WARNING: ${AppConfig.configValidationMessage}');
    debugPrint('Please update your ZEGOCloud credentials in lib/config/app_config.dart');
  }

  // Initialize services
  await AuthenticationService().initialize();
  await CallService().initialize();

  // Initialize ZEGOCloud (only if config is valid)
  if (AppConfig.isConfigValid) {
    try {
      await ZegoConfig.initialize();
    } catch (e) {
      debugPrint('ZEGOCloud initialization failed: $e');
    }
  }

  runApp(const CMTConnectApp());
}

/// Request necessary permissions for the app
Future<void> _requestPermissions() async {
  final permissions = [
    Permission.camera,
    Permission.microphone,
    Permission.notification,
  ];

  for (final permission in permissions) {
    final status = await permission.status;
    if (!status.isGranted) {
      await permission.request();
    }
  }
}
