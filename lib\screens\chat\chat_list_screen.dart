import 'package:flutter/material.dart';
import '../../models/message.dart';
import '../../models/user.dart';
import '../../services/chat_service.dart';
import '../../services/authentication_service.dart';
import '../../widgets/user_selection_dialog.dart';
import 'chat_screen.dart';

/// Chat list screen showing conversations
class ChatListScreen extends StatefulWidget {
  const ChatListScreen({super.key});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  final ChatService _chatService = ChatService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chats'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_comment),
            onPressed: _startNewChat,
          ),
        ],
      ),
      body: StreamBuilder<Map<String, List<Message>>>(
        stream: _chatService.conversationStream.map((_) => _chatService.getAllConversations()),
        initialData: _chatService.getAllConversations(),
        builder: (context, snapshot) {
          final conversations = snapshot.data ?? {};

          if (conversations.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 80,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No conversations yet',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Start a conversation to see it here',
                    style: TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: conversations.length,
            itemBuilder: (context, index) {
              final conversationId = conversations.keys.elementAt(index);
              final messages = conversations[conversationId]!;
              final lastMessage = messages.isNotEmpty ? messages.last : null;

              return _buildConversationItem(conversationId, lastMessage);
            },
          );
        },
      ),
    );
  }

  Widget _buildConversationItem(String conversationId, Message? lastMessage) {
    final currentUser = AuthenticationService().currentUser;
    if (currentUser == null) return const SizedBox.shrink();

    // Extract other user ID from conversation ID
    final userIds = conversationId.split('_');
    final otherUserId = userIds.firstWhere((id) => id != currentUser.id);

    // Get user info from test users
    final testUser = TestUser.values.where((tu) => tu.id == otherUserId).firstOrNull;
    final userName = testUser?.name ?? 'User ${otherUserId.substring(0, 8)}';
    final userAvatar = testUser?.avatarUrl;

    return ListTile(
      leading: CircleAvatar(
        backgroundImage: userAvatar != null ? NetworkImage(userAvatar) : null,
        child: userAvatar == null ? Text(userName.substring(0, 1).toUpperCase()) : null,
      ),
      title: Text(
        userName,
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: lastMessage != null
          ? Text(
              lastMessage.content,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: lastMessage.status == MessageStatus.unread
                    ? Colors.black87
                    : Colors.grey[600],
                fontWeight: lastMessage.status == MessageStatus.unread
                    ? FontWeight.w500
                    : FontWeight.normal,
              ),
            )
          : const Text('No messages yet'),
      trailing: lastMessage != null
          ? Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  _formatTime(lastMessage.createdAt),
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 12,
                  ),
                ),
                if (lastMessage.status == MessageStatus.unread)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            )
          : null,
      onTap: () => _openChat(otherUserId, userName),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      // Today - show time
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      // This week - show day
      final weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return weekdays[dateTime.weekday - 1];
    } else {
      // Older - show date
      return '${dateTime.day}/${dateTime.month}';
    }
  }

  void _startNewChat() async {
    final users = await showUserSelectionDialog(
      context: context,
      title: 'Start new chat',
      allowMultipleSelection: false,
    );

    if (users != null && users.isNotEmpty) {
      final user = users.first;
      _openChat(user.id, user.name);
    }
  }

  void _openChat(String userId, String userName) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          userId: userId,
          userName: userName,
        ),
      ),
    );
  }
}
