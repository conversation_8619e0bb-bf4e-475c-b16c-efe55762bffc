import 'dart:async';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/user.dart';
import '../config/app_config.dart';
import '../config/zego_config.dart';

/// Service for handling user authentication and session management
class AuthenticationService {
  static final AuthenticationService _instance = AuthenticationService._internal();
  factory AuthenticationService() => _instance;
  AuthenticationService._internal();

  final StreamController<User?> _userController = StreamController<User?>.broadcast();
  final StreamController<bool> _authStateController = StreamController<bool>.broadcast();
  
  User? _currentUser;
  SharedPreferences? _prefs;

  /// Stream of current user changes
  Stream<User?> get userStream => _userController.stream;
  
  /// Stream of authentication state changes
  Stream<bool> get authStateStream => _authStateController.stream;
  
  /// Get current authenticated user
  User? get currentUser => _currentUser;
  
  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// Initialize the authentication service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSavedUser();
  }

  /// Load saved user from local storage
  Future<void> _loadSavedUser() async {
    if (_prefs == null) return;

    final isLoggedIn = _prefs!.getBool(AppConfig.isLoggedInKey) ?? false;
    if (!isLoggedIn) return;

    final userId = _prefs!.getString(AppConfig.userIdKey);
    final userName = _prefs!.getString(AppConfig.userNameKey);
    final userAvatar = _prefs!.getString(AppConfig.userAvatarKey);

    if (userId != null && userName != null) {
      _currentUser = User(
        id: userId,
        name: userName,
        avatarUrl: userAvatar,
        status: UserStatus.online,
        isOnline: true,
      );

      // Update ZEGOCloud user info
      await ZegoConfig.updateUserInfo(
        userID: userId,
        userName: userName,
        avatarUrl: userAvatar,
      );

      _userController.add(_currentUser);
      _authStateController.add(true);
    }
  }

  /// Login with username (demo implementation)
  Future<User> loginWithUsername(String username) async {
    if (username.trim().isEmpty) {
      throw Exception('Username cannot be empty');
    }

    // Generate a unique user ID
    final userId = const Uuid().v4();
    
    // Create user object
    final user = User(
      id: userId,
      name: username.trim(),
      status: UserStatus.online,
      isOnline: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _saveUserSession(user);
    return user;
  }

  /// Login as guest user
  Future<User> loginAsGuest() async {
    final guestNumber = Random().nextInt(9999) + 1;
    final username = 'Guest$guestNumber';
    return await loginWithUsername(username);
  }

  /// Login with predefined test users
  Future<User> loginWithTestUser(TestUser testUser) async {
    final user = User(
      id: testUser.id,
      name: testUser.name,
      email: testUser.email,
      avatarUrl: testUser.avatarUrl,
      status: UserStatus.online,
      isOnline: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _saveUserSession(user);
    return user;
  }

  /// Save user session to local storage
  Future<void> _saveUserSession(User user) async {
    if (_prefs == null) return;

    await _prefs!.setBool(AppConfig.isLoggedInKey, true);
    await _prefs!.setString(AppConfig.userIdKey, user.id);
    await _prefs!.setString(AppConfig.userNameKey, user.name);
    if (user.avatarUrl != null) {
      await _prefs!.setString(AppConfig.userAvatarKey, user.avatarUrl!);
    }
    await _prefs!.setString(AppConfig.lastLoginKey, DateTime.now().toIso8601String());

    // Update ZEGOCloud user info
    await ZegoConfig.updateUserInfo(
      userID: user.id,
      userName: user.name,
      avatarUrl: user.avatarUrl,
    );

    _currentUser = user;
    _userController.add(_currentUser);
    _authStateController.add(true);
  }

  /// Update user profile
  Future<User> updateProfile({
    String? name,
    String? email,
    String? avatarUrl,
    String? phoneNumber,
  }) async {
    if (_currentUser == null) {
      throw Exception('No user is currently logged in');
    }

    final updatedUser = _currentUser!.copyWith(
      name: name,
      email: email,
      avatarUrl: avatarUrl,
      phoneNumber: phoneNumber,
      updatedAt: DateTime.now(),
    );

    await _saveUserSession(updatedUser);
    return updatedUser;
  }

  /// Update user status
  Future<void> updateUserStatus(UserStatus status) async {
    if (_currentUser == null) return;

    final updatedUser = _currentUser!.copyWith(
      status: status,
      isOnline: status != UserStatus.offline,
      lastSeen: status == UserStatus.offline ? DateTime.now() : null,
      updatedAt: DateTime.now(),
    );

    _currentUser = updatedUser;
    _userController.add(_currentUser);

    // Save to local storage
    if (_prefs != null) {
      await _prefs!.setString(AppConfig.userIdKey, updatedUser.id);
      await _prefs!.setString(AppConfig.userNameKey, updatedUser.name);
    }
  }

  /// Logout user
  Future<void> logout() async {
    if (_prefs != null) {
      await _prefs!.setBool(AppConfig.isLoggedInKey, false);
      await _prefs!.remove(AppConfig.userIdKey);
      await _prefs!.remove(AppConfig.userNameKey);
      await _prefs!.remove(AppConfig.userAvatarKey);
    }

    // Uninitialize ZEGOCloud services
    await ZegoConfig.uninitialize();

    _currentUser = null;
    _userController.add(null);
    _authStateController.add(false);
  }

  /// Dispose the service
  void dispose() {
    _userController.close();
    _authStateController.close();
  }
}

/// Predefined test users for demo purposes
enum TestUser {
  alice('alice_001', 'Alice Johnson', '<EMAIL>', 'https://i.pravatar.cc/150?img=1'),
  bob('bob_002', 'Bob Smith', '<EMAIL>', 'https://i.pravatar.cc/150?img=2'),
  charlie('charlie_003', 'Charlie Brown', '<EMAIL>', 'https://i.pravatar.cc/150?img=3'),
  diana('diana_004', 'Diana Prince', '<EMAIL>', 'https://i.pravatar.cc/150?img=4'),
  eve('eve_005', 'Eve Wilson', '<EMAIL>', 'https://i.pravatar.cc/150?img=5');

  const TestUser(this.id, this.name, this.email, this.avatarUrl);

  final String id;
  final String name;
  final String email;
  final String avatarUrl;
}
