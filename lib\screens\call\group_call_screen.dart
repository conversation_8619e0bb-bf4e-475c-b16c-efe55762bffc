import 'package:flutter/material.dart';
import 'package:zego_uikit_prebuilt_video_conference/zego_uikit_prebuilt_video_conference.dart';
import '../../config/app_config.dart';
import '../../models/user.dart';
import '../../services/authentication_service.dart';

/// Screen for group video conference calls
class GroupCallScreen extends StatelessWidget {
  final String conferenceID;
  final List<User> participants;
  final bool isVideoEnabled;

  const GroupCallScreen({
    super.key,
    required this.conferenceID,
    required this.participants,
    this.isVideoEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final currentUser = AuthenticationService().currentUser;
    
    if (currentUser == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Group Call')),
        body: const Center(
          child: Text('User not authenticated'),
        ),
      );
    }

    if (!AppConfig.isConfigValid) {
      return Scaffold(
        appBar: AppBar(title: const Text('Group Call')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.warning,
                size: 80,
                color: Colors.orange,
              ),
              SizedBox(height: 16),
              Text(
                'ZEGOCloud not configured',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Please configure ZEGOCloud credentials to use group calls',
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SafeArea(
      child: ZegoUIKitPrebuiltVideoConference(
        appID: AppConfig.zegoAppID,
        appSign: AppConfig.zegoAppSign,
        userID: currentUser.id,
        userName: currentUser.name,
        conferenceID: conferenceID,
        config: ZegoUIKitPrebuiltVideoConferenceConfig(
          turnOnCameraWhenJoining: isVideoEnabled,
          turnOnMicrophoneWhenJoining: true,
          useSpeakerWhenJoining: true,
          onLeave: () {
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }
}

/// Helper function to navigate to group call screen
void navigateToGroupCall({
  required BuildContext context,
  required String conferenceID,
  required List<User> participants,
  bool isVideoEnabled = true,
}) {
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => GroupCallScreen(
        conferenceID: conferenceID,
        participants: participants,
        isVideoEnabled: isVideoEnabled,
      ),
    ),
  );
}

/// Helper function to generate conference ID
String generateConferenceID() {
  // Generate a simple conference ID based on timestamp
  return 'conf_${DateTime.now().millisecondsSinceEpoch}';
}
