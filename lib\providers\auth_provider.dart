import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/authentication_service.dart';

/// Provider for managing authentication state
class AuthProvider extends ChangeNotifier {
  final AuthenticationService _authService = AuthenticationService();
  
  User? _currentUser;
  bool _isLoading = false;
  String? _error;

  /// Get current user
  User? get currentUser => _currentUser;
  
  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;
  
  /// Check if loading
  bool get isLoading => _isLoading;
  
  /// Get error message
  String? get error => _error;

  /// Initialize the provider
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _authService.initialize();
      _currentUser = _authService.currentUser;
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }

    // Listen to auth state changes
    _authService.userStream.listen((user) {
      _currentUser = user;
      notifyListeners();
    });
  }

  /// Login with username
  Future<bool> loginWithUsername(String username) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final user = await _authService.loginWithUsername(username);
      _currentUser = user;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Login as guest
  Future<bool> loginAsGuest() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final user = await _authService.loginAsGuest();
      _currentUser = user;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Login with test user
  Future<bool> loginWithTestUser(TestUser testUser) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final user = await _authService.loginWithTestUser(testUser);
      _currentUser = user;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Update user profile
  Future<bool> updateProfile({
    String? name,
    String? email,
    String? avatarUrl,
    String? phoneNumber,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final user = await _authService.updateProfile(
        name: name,
        email: email,
        avatarUrl: avatarUrl,
        phoneNumber: phoneNumber,
      );
      _currentUser = user;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Update user status
  Future<void> updateUserStatus(UserStatus status) async {
    try {
      await _authService.updateUserStatus(status);
      // User will be updated through the stream listener
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Logout
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _authService.logout();
      _currentUser = null;
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _authService.dispose();
    super.dispose();
  }
}
