name: cmt_connect_zegocloud_poc
description: "A comprehensive Flutter PoC for event organization platform with ZEGOCloud real-time communication features."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.8

  # ZEGOCloud SDKs - Core Communication Features
  zego_uikit_prebuilt_call: ^4.17.1      # Call Kit with invitation
  zego_zim: ^2.20.0+1                    # In-app Chat (ZIM SDK)
  zego_express_engine: ^3.20.5           # Real-time Communication
  zego_uikit_signaling_plugin: ^2.8.13   # Signaling for presence
  zego_zpns: ^2.6.0+3                    # Push notifications

  # Additional ZEGOCloud UIKits
  zego_uikit_prebuilt_video_conference: ^2.10.0  # Group meetings
  zego_uikit_prebuilt_live_streaming: ^3.14.1    # Live streaming

  # State Management
  provider: ^6.1.2

  # Local Storage & Preferences
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Permissions
  permission_handler: ^11.3.1

  # Network & HTTP
  http: ^1.2.1
  dio: ^5.4.3+1

  # Utils
  uuid: ^4.4.0
  intl: ^0.19.0

  # Firebase for Push Notifications
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3

  # Device Info
  device_info_plus: ^11.3.0
  package_info_plus: ^8.0.2

  # Audio/Video utilities
  wakelock_plus: ^1.2.8

  # UI Enhancements
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.9

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/sounds/
