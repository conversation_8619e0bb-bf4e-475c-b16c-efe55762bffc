import 'package:flutter/material.dart';
import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';
import 'package:zego_uikit_signaling_plugin/zego_uikit_signaling_plugin.dart';
import 'app_config.dart';

/// ZEGOCloud SDK configuration and initialization
class ZegoConfig {
  static bool _isInitialized = false;

  /// Initialize ZEGOCloud services
  static Future<void> initialize() async {
    if (!AppConfig.isConfigValid) {
      throw Exception(AppConfig.configValidationMessage);
    }

    if (_isInitialized) {
      debugPrint('ZEGOCloud already initialized');
      return;
    }

    try {
      // Initialize ZEGOUIKitPrebuiltCallInvitationService
      ZegoUIKitPrebuiltCallInvitationService().init(
        appID: AppConfig.zegoAppID,
        appSign: AppConfig.zegoAppSign,
        userID: '',
        userName: '',
        plugins: [ZegoUIKitSignalingPlugin()],
      );

      _isInitialized = true;
      debugPrint('✅ ZEGOCloud services initialized successfully');
    } catch (e) {
      debugPrint('❌ ZEGOCloud initialization failed: $e');
      rethrow;
    }
  }

  /// Update user information for ZEGOCloud services
  static Future<void> updateUserInfo({
    required String userID,
    required String userName,
    String? avatarUrl,
  }) async {
    if (!AppConfig.isConfigValid) {
      debugPrint('⚠️ ZEGOCloud not configured, skipping user info update');
      return;
    }

    try {
      // Update user info for call invitation service
      await ZegoUIKitPrebuiltCallInvitationService().init(
        appID: AppConfig.zegoAppID,
        appSign: AppConfig.zegoAppSign,
        userID: userID,
        userName: userName,
        plugins: [ZegoUIKitSignalingPlugin()],
      );

      debugPrint('✅ ZEGOCloud user info updated: $userID - $userName');
    } catch (e) {
      debugPrint('❌ Failed to update ZEGOCloud user info: $e');
    }
  }

  /// Uninitialize ZEGOCloud services
  static Future<void> uninitialize() async {
    try {
      await ZegoUIKitPrebuiltCallInvitationService().uninit();
      _isInitialized = false;
      debugPrint('✅ ZEGOCloud services uninitialized');
    } catch (e) {
      debugPrint('❌ ZEGOCloud uninitialize failed: $e');
    }
  }
}
