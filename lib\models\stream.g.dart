// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stream.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LiveStreamAdapter extends TypeAdapter<LiveStream> {
  @override
  final int typeId = 9;

  @override
  LiveStream read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LiveStream(
      id: fields[0] as String,
      hostId: fields[1] as String,
      title: fields[2] as String,
      description: fields[3] as String?,
      thumbnailUrl: fields[4] as String?,
      status: fields[5] as StreamStatus,
      quality: fields[6] as StreamQuality,
      createdAt: fields[7] as DateTime?,
      startedAt: fields[8] as DateTime?,
      endedAt: fields[9] as DateTime?,
      viewerCount: fields[10] as int,
      maxViewers: fields[11] as int,
      duration: fields[12] as int?,
      tags: (fields[13] as List).cast<String>(),
      isPrivate: fields[14] as bool,
      password: fields[15] as String?,
      metadata: (fields[16] as Map?)?.cast<String, dynamic>(),
      allowComments: fields[17] as bool,
      allowReactions: fields[18] as bool,
      category: fields[19] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, LiveStream obj) {
    writer
      ..writeByte(20)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.hostId)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.thumbnailUrl)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.quality)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.startedAt)
      ..writeByte(9)
      ..write(obj.endedAt)
      ..writeByte(10)
      ..write(obj.viewerCount)
      ..writeByte(11)
      ..write(obj.maxViewers)
      ..writeByte(12)
      ..write(obj.duration)
      ..writeByte(13)
      ..write(obj.tags)
      ..writeByte(14)
      ..write(obj.isPrivate)
      ..writeByte(15)
      ..write(obj.password)
      ..writeByte(16)
      ..write(obj.metadata)
      ..writeByte(17)
      ..write(obj.allowComments)
      ..writeByte(18)
      ..write(obj.allowReactions)
      ..writeByte(19)
      ..write(obj.category);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LiveStreamAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class StreamStatusAdapter extends TypeAdapter<StreamStatus> {
  @override
  final int typeId = 10;

  @override
  StreamStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return StreamStatus.scheduled;
      case 1:
        return StreamStatus.live;
      case 2:
        return StreamStatus.ended;
      case 3:
        return StreamStatus.paused;
      default:
        return StreamStatus.scheduled;
    }
  }

  @override
  void write(BinaryWriter writer, StreamStatus obj) {
    switch (obj) {
      case StreamStatus.scheduled:
        writer.writeByte(0);
        break;
      case StreamStatus.live:
        writer.writeByte(1);
        break;
      case StreamStatus.ended:
        writer.writeByte(2);
        break;
      case StreamStatus.paused:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreamStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class StreamQualityAdapter extends TypeAdapter<StreamQuality> {
  @override
  final int typeId = 11;

  @override
  StreamQuality read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return StreamQuality.low;
      case 1:
        return StreamQuality.medium;
      case 2:
        return StreamQuality.hd;
      case 3:
        return StreamQuality.fullHd;
      case 4:
        return StreamQuality.uhd;
      default:
        return StreamQuality.low;
    }
  }

  @override
  void write(BinaryWriter writer, StreamQuality obj) {
    switch (obj) {
      case StreamQuality.low:
        writer.writeByte(0);
        break;
      case StreamQuality.medium:
        writer.writeByte(1);
        break;
      case StreamQuality.hd:
        writer.writeByte(2);
        break;
      case StreamQuality.fullHd:
        writer.writeByte(3);
        break;
      case StreamQuality.uhd:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreamQualityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
