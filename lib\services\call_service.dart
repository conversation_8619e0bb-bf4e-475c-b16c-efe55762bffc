import 'dart:async';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';
import 'package:zego_uikit_prebuilt_video_conference/zego_uikit_prebuilt_video_conference.dart';
import '../models/call.dart';
import '../models/user.dart';
import '../config/app_config.dart';
import 'authentication_service.dart';

/// Service for handling voice and video calls (Demo Implementation)
class CallService {
  static final CallService _instance = CallService._internal();
  factory CallService() => _instance;
  CallService._internal();

  final StreamController<Call> _callStateController = StreamController<Call>.broadcast();
  final StreamController<List<Call>> _callHistoryController = StreamController<List<Call>>.broadcast();
  
  final List<Call> _callHistory = [];
  Call? _currentCall;

  /// Stream of call state changes
  Stream<Call> get callStateStream => _callStateController.stream;
  
  /// Stream of call history changes
  Stream<List<Call>> get callHistoryStream => _callHistoryController.stream;
  
  /// Get current active call
  Call? get currentCall => _currentCall;
  
  /// Get call history
  List<Call> get callHistory => List.unmodifiable(_callHistory);

  /// Initialize the call service
  Future<void> initialize() async {
    // TODO: Set up call invitation listeners when ZEGOCloud is properly configured
  }

  /// Make a voice call using ZEGOCloud
  Future<void> makeVoiceCall({
    required String targetUserId,
    required String targetUserName,
    String? targetUserAvatar,
  }) async {
    final currentUser = AuthenticationService().currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    if (!AppConfig.isConfigValid) {
      // Fallback to demo mode
      await _makeDemoCall(
        targetUserId: targetUserId,
        targetUserName: targetUserName,
        isVideo: false,
      );
      return;
    }

    try {
      // Create call record
      final callId = const Uuid().v4();
      final call = Call(
        id: callId,
        callerId: currentUser.id,
        participantIds: [currentUser.id, targetUserId],
        type: CallType.voice,
        status: CallStatus.pending,
        isVideoEnabled: false,
        isAudioEnabled: true,
      );

      _currentCall = call;
      _addToCallHistory(call);

      // Send call invitation using ZEGOCloud
      await ZegoUIKitPrebuiltCallInvitationService().send(
        isVideoCall: false,
        invitees: [
          ZegoCallUser(
            targetUserId,
            targetUserName,
          ),
        ],
        customData: callId,
      );

      debugPrint('✅ Voice call invitation sent to $targetUserName');
    } catch (e) {
      debugPrint('❌ Failed to send voice call invitation: $e');
      // Fallback to demo mode
      await _makeDemoCall(
        targetUserId: targetUserId,
        targetUserName: targetUserName,
        isVideo: false,
      );
    }
  }

  /// Make a video call using ZEGOCloud
  Future<void> makeVideoCall({
    required String targetUserId,
    required String targetUserName,
    String? targetUserAvatar,
  }) async {
    final currentUser = AuthenticationService().currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    if (!AppConfig.isConfigValid) {
      // Fallback to demo mode
      await _makeDemoCall(
        targetUserId: targetUserId,
        targetUserName: targetUserName,
        isVideo: true,
      );
      return;
    }

    try {
      // Create call record
      final callId = const Uuid().v4();
      final call = Call(
        id: callId,
        callerId: currentUser.id,
        participantIds: [currentUser.id, targetUserId],
        type: CallType.video,
        status: CallStatus.pending,
        isVideoEnabled: true,
        isAudioEnabled: true,
      );

      _currentCall = call;
      _addToCallHistory(call);

      // Send call invitation using ZEGOCloud
      await ZegoUIKitPrebuiltCallInvitationService().send(
        isVideoCall: true,
        invitees: [
          ZegoCallUser(
            targetUserId,
            targetUserName,
          ),
        ],
        customData: callId,
      );

      debugPrint('✅ Video call invitation sent to $targetUserName');
    } catch (e) {
      debugPrint('❌ Failed to send video call invitation: $e');
      // Fallback to demo mode
      await _makeDemoCall(
        targetUserId: targetUserId,
        targetUserName: targetUserName,
        isVideo: true,
      );
    }
  }

  /// Make a group call using ZEGOCloud Video Conference
  Future<void> makeGroupCall({
    required List<User> participants,
    bool isVideoCall = true,
  }) async {
    final currentUser = AuthenticationService().currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final callId = const Uuid().v4();
    final participantIds = [currentUser.id, ...participants.map((u) => u.id)];

    final call = Call(
      id: callId,
      callerId: currentUser.id,
      participantIds: participantIds,
      type: CallType.conference,
      status: CallStatus.pending,
      isVideoEnabled: isVideoCall,
      isAudioEnabled: true,
    );

    _currentCall = call;
    _addToCallHistory(call);

    if (!AppConfig.isConfigValid) {
      // Fallback to demo mode
      await _simulateCall(call);
      return;
    }

    try {
      // For group calls, we'll use a different approach
      // The actual navigation to group call screen will be handled by the UI
      debugPrint('✅ Group call created with ${participants.length} participants');
      _updateCallStatus(CallStatus.connected);
      _currentCall = _currentCall!.copyWith(startedAt: DateTime.now());
    } catch (e) {
      debugPrint('❌ Failed to create group call: $e');
      // Fallback to demo mode
      await _simulateCall(call);
    }
  }

  /// End current call
  Future<void> endCall() async {
    if (_currentCall == null) return;
    _endCall(CallEndReason.hangup);
  }

  /// Make a demo call (fallback when ZEGOCloud is not configured)
  Future<void> _makeDemoCall({
    required String targetUserId,
    required String targetUserName,
    required bool isVideo,
  }) async {
    final currentUser = AuthenticationService().currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final callId = const Uuid().v4();
    final call = Call(
      id: callId,
      callerId: currentUser.id,
      participantIds: [currentUser.id, targetUserId],
      type: isVideo ? CallType.video : CallType.voice,
      status: CallStatus.pending,
      isVideoEnabled: isVideo,
      isAudioEnabled: true,
    );

    _currentCall = call;
    _addToCallHistory(call);

    // Simulate call behavior for demo
    await _simulateCall(call);
  }

  /// Simulate call behavior for demo
  Future<void> _simulateCall(Call call) async {
    // Simulate ringing
    await Future.delayed(const Duration(seconds: 1));
    _updateCallStatus(CallStatus.ringing);

    // Simulate call being answered after 3 seconds
    await Future.delayed(const Duration(seconds: 3));
    _updateCallStatus(CallStatus.connected);
    _currentCall = _currentCall!.copyWith(startedAt: DateTime.now());

    // Simulate call duration (auto-end after 30 seconds for demo)
    await Future.delayed(const Duration(seconds: 30));
    if (_currentCall?.status == CallStatus.connected) {
      _endCall(CallEndReason.hangup);
    }
  }

  /// Internal method to end call
  void _endCall(CallEndReason reason) {
    if (_currentCall == null) return;

    final endTime = DateTime.now();
    final duration = _currentCall!.startedAt != null 
        ? endTime.difference(_currentCall!.startedAt!).inSeconds 
        : 0;

    _currentCall = _currentCall!.copyWith(
      status: CallStatus.ended,
      endedAt: endTime,
      duration: duration,
      endReason: reason,
    );

    _updateCallInHistory(_currentCall!);
    _callStateController.add(_currentCall!);
    _currentCall = null;
  }

  /// Update call status
  void _updateCallStatus(CallStatus status) {
    if (_currentCall == null) return;

    _currentCall = _currentCall!.copyWith(status: status);
    _updateCallInHistory(_currentCall!);
    _callStateController.add(_currentCall!);
  }

  /// Add call to history
  void _addToCallHistory(Call call) {
    _callHistory.insert(0, call);
    _callHistoryController.add(_callHistory);
  }

  /// Update call in history
  void _updateCallInHistory(Call updatedCall) {
    final index = _callHistory.indexWhere((c) => c.id == updatedCall.id);
    if (index != -1) {
      _callHistory[index] = updatedCall;
      _callHistoryController.add(_callHistory);
    }
  }

  /// Dispose the service
  void dispose() {
    _callStateController.close();
    _callHistoryController.close();
  }
}
