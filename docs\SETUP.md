# CMT Connect - Detailed Setup Guide

This guide provides step-by-step instructions for setting up the CMT Connect ZEGOCloud PoC application.

## Prerequisites

### Development Environment

1. **Flutter SDK**: Version 3.27.1 or later
   ```bash
   flutter --version
   ```

2. **Dart SDK**: Version 3.6.0 or later (included with Flutter)

3. **IDE**: Android Studio, VS Code, or IntelliJ IDEA

4. **Platform Tools**:
   - Android: Android Studio with SDK
   - iOS: Xcode (macOS only)

### ZEGOCloud Account

1. Visit [ZEGOCloud Console](https://console.zegocloud.com/)
2. Sign up for a free account
3. Create a new project
4. Note down your App ID and App Sign

## Step-by-Step Setup

### 1. Project Setup

```bash
# Clone the repository
git clone <repository-url>
cd cmt_connect_zegocloud_poc

# Install dependencies
flutter pub get

# Generate Hive adapters
flutter packages pub run build_runner build
```

### 2. ZEGOCloud Configuration

#### Update Configuration File

Edit `lib/config/app_config.dart`:

```dart
class AppConfig {
  // Replace these with your actual ZEGOCloud credentials
  static const int zegoAppID = *********; // Your App ID
  static const String zegoAppSign = 'your_app_sign_here'; // Your App Sign
  
  // ... rest of the configuration
}
```

#### Verify Configuration

The app will show a warning if credentials are not configured properly.

### 3. Android Setup

#### Minimum Requirements

- Android API Level 21 (Android 5.0) or higher
- Target SDK: Latest stable

#### Configuration Files

The following files are already configured:

**android/app/build.gradle**:
```gradle
android {
    compileSdk 34
    defaultConfig {
        minSdk 21
        targetSdk 34
        multiDexEnabled true
    }
}
```

**android/app/src/main/AndroidManifest.xml**:
```xml
<!-- Required permissions are already included -->
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
<!-- ... other permissions -->
```

#### Testing on Android

```bash
# Connect Android device or start emulator
flutter devices

# Run on Android
flutter run
```

### 4. iOS Setup

#### Minimum Requirements

- iOS 12.0 or higher
- Xcode 14.0 or higher (for development)

#### Configuration Files

The following files are already configured:

**ios/Runner/Info.plist**:
```xml
<!-- Required permissions are already included -->
<key>NSCameraUsageDescription</key>
<string>This app needs access to camera for video calls</string>
<key>NSMicrophoneUsageDescription</key>
<string>This app needs access to microphone for calls</string>
<!-- ... other permissions -->
```

#### Testing on iOS

```bash
# Open iOS project in Xcode (optional)
open ios/Runner.xcworkspace

# Run on iOS simulator or device
flutter run
```

### 5. Firebase Setup (Optional)

For push notifications support:

#### Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. Add Android and/or iOS apps

#### Android Firebase Setup

1. Download `google-services.json`
2. Place it in `android/app/`
3. The required dependencies are already in `pubspec.yaml`

#### iOS Firebase Setup

1. Download `GoogleService-Info.plist`
2. Add it to the iOS project in Xcode
3. Ensure it's added to the target

### 6. Testing the Setup

#### Run the Application

```bash
# Debug mode
flutter run

# Release mode (for performance testing)
flutter run --release
```

#### Verify Features

1. **Authentication**: Try logging in with test users
2. **UI Navigation**: Navigate through different tabs
3. **Permissions**: Check camera/microphone permissions
4. **ZEGOCloud**: Verify no initialization errors in logs

#### Test Users

Use these predefined test accounts:

- **Alice Johnson**: alice_001
- **Bob Smith**: bob_002
- **Charlie Brown**: charlie_003
- **Diana Prince**: diana_004
- **Eve Wilson**: eve_005

## Troubleshooting

### Common Issues

#### 1. Build Errors

**Problem**: Dependency conflicts or build failures

**Solution**:
```bash
flutter clean
flutter pub get
flutter packages pub run build_runner build --delete-conflicting-outputs
```

#### 2. ZEGOCloud Initialization Fails

**Problem**: App shows configuration warning

**Solutions**:
- Verify App ID and App Sign are correct
- Check network connectivity
- Ensure ZEGOCloud account is active

#### 3. Permission Denied

**Problem**: Camera/microphone not working

**Solutions**:
- Check device permissions in settings
- Test on physical device (not simulator for some features)
- Verify platform-specific permission configurations

#### 4. iOS Build Issues

**Problem**: iOS build fails or crashes

**Solutions**:
- Update Xcode to latest version
- Clean iOS build: `flutter clean && cd ios && rm -rf Pods Podfile.lock && pod install`
- Check iOS deployment target (minimum iOS 12.0)

#### 5. Android Build Issues

**Problem**: Android build fails

**Solutions**:
- Check Android SDK is properly installed
- Verify minimum SDK version (21)
- Enable multidex if getting method count errors

### Debug Mode

Enable detailed logging by updating `app_config.dart`:

```dart
static const bool enableLogging = true;
static const bool enableDebugMode = true;
```

### Performance Testing

For optimal performance testing:

1. Use release builds: `flutter run --release`
2. Test on physical devices
3. Monitor memory usage during calls
4. Test network interruption scenarios

## Development Workflow

### Adding New Features

1. Create feature branch
2. Implement in appropriate service layer
3. Add UI components
4. Update state management
5. Test thoroughly
6. Update documentation

### Code Structure

Follow the established patterns:

- **Services**: Business logic and external API integration
- **Providers**: State management using Provider pattern
- **Models**: Data structures with Hive serialization
- **Screens**: UI components organized by feature
- **Config**: Environment and feature configurations

## Production Considerations

Before deploying to production:

1. **Security**: Implement proper credential management
2. **Error Handling**: Add comprehensive error handling
3. **Analytics**: Integrate analytics and crash reporting
4. **Testing**: Add unit and integration tests
5. **Performance**: Optimize for production workloads
6. **Compliance**: Ensure privacy and data protection compliance

## Support

For additional help:

1. Check the main README.md
2. Review ZEGOCloud documentation
3. Check Flutter documentation
4. Create an issue in the repository

---

**Next Steps**: After setup, refer to the main README.md for usage instructions and feature documentation.
