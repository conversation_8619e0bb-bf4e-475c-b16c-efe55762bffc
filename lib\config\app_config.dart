/// Application configuration and environment settings
class AppConfig {
  // Environment settings
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );
  
  static bool get isDevelopment => environment == 'development';
  static bool get isProduction => environment == 'production';
  
  // App Information
  static const String appName = 'CMT Connect';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // ZEGOCloud Configuration
  // NOTE: Replace these with your actual ZEGOCloud credentials
  // Get them from: https://console.zegocloud.com/
  static const int zegoAppID = 0; // Replace with your App ID
  static const String zegoAppSign = ''; // Replace with your App Sign
  
  // Server Configuration (if using custom backend)
  static String get baseUrl {
    switch (environment) {
      case 'production':
        return 'https://api.cmtconnect.com';
      case 'staging':
        return 'https://staging-api.cmtconnect.com';
      default:
        return 'https://dev-api.cmtconnect.com';
    }
  }
  
  // Feature Flags
  static const bool enablePushNotifications = true;
  static const bool enableCallKit = true;
  static const bool enableLiveStreaming = true;
  static const bool enableGroupCalls = true;
  static const bool enableScreenSharing = true;
  static const bool enableBeautyEffects = false; // Premium feature
  
  // Call Configuration
  static const int maxCallDuration = 3600; // 1 hour in seconds
  static const int callTimeoutDuration = 30; // 30 seconds
  static const int maxGroupCallParticipants = 12;
  
  // Chat Configuration
  static const int maxMessageLength = 1000;
  static const int chatHistoryLimit = 100;
  static const bool enableMessageEncryption = true;
  
  // Streaming Configuration
  static const int maxStreamDuration = 7200; // 2 hours in seconds
  static const int maxConcurrentViewers = 1000;
  static const String defaultStreamQuality = 'HD';
  
  // UI Configuration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashScreenDuration = Duration(seconds: 2);
  
  // Storage Keys
  static const String userIdKey = 'user_id';
  static const String userNameKey = 'user_name';
  static const String userAvatarKey = 'user_avatar';
  static const String isLoggedInKey = 'is_logged_in';
  static const String fcmTokenKey = 'fcm_token';
  static const String lastLoginKey = 'last_login';
  
  // Validation
  static bool get isConfigValid {
    return zegoAppID != 0 && zegoAppSign.isNotEmpty;
  }
  
  static String get configValidationMessage {
    if (zegoAppID == 0) {
      return 'ZEGOCloud App ID is not configured. Please update AppConfig.zegoAppID';
    }
    if (zegoAppSign.isEmpty) {
      return 'ZEGOCloud App Sign is not configured. Please update AppConfig.zegoAppSign';
    }
    return 'Configuration is valid';
  }
}

/// Environment-specific configurations
class EnvironmentConfig {
  static Map<String, dynamic> get config {
    switch (AppConfig.environment) {
      case 'production':
        return _productionConfig;
      case 'staging':
        return _stagingConfig;
      default:
        return _developmentConfig;
    }
  }
  
  static final Map<String, dynamic> _developmentConfig = {
    'api_timeout': 30000,
    'enable_logging': true,
    'enable_debug_mode': true,
    'enable_analytics': false,
    'cache_duration': 300, // 5 minutes
  };
  
  static final Map<String, dynamic> _stagingConfig = {
    'api_timeout': 20000,
    'enable_logging': true,
    'enable_debug_mode': false,
    'enable_analytics': true,
    'cache_duration': 600, // 10 minutes
  };
  
  static final Map<String, dynamic> _productionConfig = {
    'api_timeout': 15000,
    'enable_logging': false,
    'enable_debug_mode': false,
    'enable_analytics': true,
    'cache_duration': 1800, // 30 minutes
  };
}
