# CMT Connect - ZEGOCloud PoC

A comprehensive Flutter Proof of Concept (PoC) application for an event organization platform with real-time communication features, built exclusively with ZEGOCloud SDKs.

## 🚀 Features

### ✅ Implemented Core Features

- **User Authentication & Management**
  - Multiple login options (username, guest, test users)
  - User presence status tracking
  - Profile management

- **Real-time Communication Foundation**
  - ZEGOCloud SDK integration
  - Service layer architecture
  - State management with Provider

- **Modern UI/UX**
  - Material Design 3
  - Responsive layouts
  - Clean architecture

### 🔄 Features In Development

- **1-on-1 Voice & Video Calling**
  - Full call lifecycle management
  - Incoming call UI with background support
  - Call history tracking

- **Group Video Meetings**
  - Multi-participant conferences
  - Media controls and participant management
  - Network reconnection handling

- **Live Streaming**
  - Host and viewer functionality
  - Interactive elements and reactions
  - Stream quality adaptation

- **Real-time Messaging**
  - 1-on-1 and group chat
  - Typing indicators and message status
  - Offline message synchronization

- **Push Notifications**
  - Firebase Cloud Messaging integration
  - Background call notifications
  - Cross-platform notification handling

## 📋 Prerequisites

- Flutter 3.27.1 or later
- Dart 3.6.0 or later
- Android Studio / VS Code
- iOS development setup (for iOS builds)
- ZEGOCloud account and credentials

## 🛠️ Setup Instructions

### 1. <PERSON>lone and Install Dependencies

```bash
git clone <repository-url>
cd cmt_connect_zegocloud_poc
flutter pub get
```

### 2. ZEGOCloud Configuration

1. Sign up at [ZEGOCloud Console](https://console.zegocloud.com/)
2. Create a new project
3. Copy your App ID and App Sign
4. Update `lib/config/app_config.dart`:

```dart
static const int zegoAppID = YOUR_APP_ID; // Replace with your App ID
static const String zegoAppSign = 'YOUR_APP_SIGN'; // Replace with your App Sign
```

### 3. Generate Hive Adapters

```bash
flutter packages pub run build_runner build
```

### 4. Platform-Specific Setup

#### Android Setup
- Minimum SDK: 21 (already configured)
- Required permissions in `AndroidManifest.xml` (already included)
- Multidex enabled

#### iOS Setup
- Camera and microphone permissions in `Info.plist` (already included)
- Background modes for VoIP
- App Transport Security settings

### 5. Firebase Setup (Optional)
For push notifications:
1. Create a Firebase project
2. Add your Android/iOS apps
3. Download configuration files
4. Place them in appropriate directories

## 🏃‍♂️ Running the App

```bash
# Development mode
flutter run

# Release mode
flutter run --release
```

## 🧪 Testing

### Test Users
The app includes predefined test users:
- Alice Johnson (alice_001)
- Bob Smith (bob_002)
- Charlie Brown (charlie_003)
- Diana Prince (diana_004)
- Eve Wilson (eve_005)

### Demo Mode
If ZEGOCloud credentials are not configured, the app runs in demo mode with limited functionality.

## 📱 App Structure

```
lib/
├── config/           # Configuration files
├── models/           # Data models
├── services/         # Business logic services
├── providers/        # State management
├── screens/          # UI screens
├── widgets/          # Reusable widgets
└── main.dart         # App entry point
```

## 🚨 Troubleshooting

### Common Issues

1. **ZEGOCloud initialization fails**
   - Check App ID and App Sign in `app_config.dart`
   - Verify network connectivity

2. **Build errors**
   - Run `flutter clean && flutter pub get`
   - Regenerate Hive adapters

3. **Permission issues**
   - Check platform-specific configurations
   - Test on physical devices

## 📄 License

This project is licensed under the MIT License.

## 🔗 Resources

- [ZEGOCloud Documentation](https://docs.zegocloud.com/)
- [Flutter Documentation](https://flutter.dev/docs)
- [Firebase Documentation](https://firebase.google.com/docs)

---

**Note**: This is a Proof of Concept application. For production use, additional security measures and testing should be implemented.
