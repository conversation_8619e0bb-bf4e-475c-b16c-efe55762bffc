import 'package:flutter/material.dart';
import '../models/user.dart';
import '../services/authentication_service.dart';

/// Dialog for selecting users for calls or chats
class UserSelectionDialog extends StatefulWidget {
  final String title;
  final bool allowMultipleSelection;
  final List<User> excludeUsers;
  final Function(List<User>) onUsersSelected;

  const UserSelectionDialog({
    super.key,
    required this.title,
    required this.onUsersSelected,
    this.allowMultipleSelection = false,
    this.excludeUsers = const [],
  });

  @override
  State<UserSelectionDialog> createState() => _UserSelectionDialogState();
}

class _UserSelectionDialogState extends State<UserSelectionDialog> {
  final List<User> _selectedUsers = [];
  final List<User> _availableUsers = [];
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAvailableUsers();
  }

  void _loadAvailableUsers() {
    // Get predefined test users
    final testUsers = TestUser.values.map((testUser) => User(
      id: testUser.id,
      name: testUser.name,
      email: testUser.email,
      avatarUrl: testUser.avatarUrl,
      status: UserStatus.online,
      isOnline: true,
    )).toList();

    // Exclude current user and specified users
    final currentUser = AuthenticationService().currentUser;
    final excludeIds = [
      if (currentUser != null) currentUser.id,
      ...widget.excludeUsers.map((u) => u.id),
    ];

    _availableUsers.clear();
    _availableUsers.addAll(
      testUsers.where((user) => !excludeIds.contains(user.id)),
    );

    setState(() {});
  }

  List<User> get _filteredUsers {
    if (_searchQuery.isEmpty) return _availableUsers;
    
    return _availableUsers.where((user) {
      return user.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             (user.email?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
    }).toList();
  }

  void _toggleUserSelection(User user) {
    setState(() {
      if (_selectedUsers.contains(user)) {
        _selectedUsers.remove(user);
      } else {
        if (widget.allowMultipleSelection) {
          _selectedUsers.add(user);
        } else {
          _selectedUsers.clear();
          _selectedUsers.add(user);
        }
      }
    });
  }

  void _onConfirm() {
    if (_selectedUsers.isNotEmpty) {
      widget.onUsersSelected(_selectedUsers);
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    widget.title,
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Search bar
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search users...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // Selected users count
            if (widget.allowMultipleSelection && _selectedUsers.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  '${_selectedUsers.length} user(s) selected',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),

            // Users list
            Expanded(
              child: _filteredUsers.isEmpty
                  ? const Center(
                      child: Text('No users found'),
                    )
                  : ListView.builder(
                      itemCount: _filteredUsers.length,
                      itemBuilder: (context, index) {
                        final user = _filteredUsers[index];
                        final isSelected = _selectedUsers.contains(user);

                        return ListTile(
                          leading: CircleAvatar(
                            backgroundImage: user.hasAvatar
                                ? NetworkImage(user.avatarUrl!)
                                : null,
                            child: !user.hasAvatar
                                ? Text(user.initials)
                                : null,
                          ),
                          title: Text(user.name),
                          subtitle: user.email != null
                              ? Text(user.email!)
                              : null,
                          trailing: widget.allowMultipleSelection
                              ? Checkbox(
                                  value: isSelected,
                                  onChanged: (_) => _toggleUserSelection(user),
                                )
                              : isSelected
                                  ? const Icon(Icons.check_circle, color: Colors.green)
                                  : null,
                          selected: isSelected,
                          onTap: () => _toggleUserSelection(user),
                        );
                      },
                    ),
            ),

            // Action buttons
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _selectedUsers.isNotEmpty ? _onConfirm : null,
                  child: const Text('Confirm'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

/// Helper function to show user selection dialog
Future<List<User>?> showUserSelectionDialog({
  required BuildContext context,
  required String title,
  bool allowMultipleSelection = false,
  List<User> excludeUsers = const [],
}) async {
  List<User>? selectedUsers;

  await showDialog<void>(
    context: context,
    builder: (context) => UserSelectionDialog(
      title: title,
      allowMultipleSelection: allowMultipleSelection,
      excludeUsers: excludeUsers,
      onUsersSelected: (users) {
        selectedUsers = users;
      },
    ),
  );

  return selectedUsers;
}
