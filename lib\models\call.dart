import 'package:hive/hive.dart';
import 'user.dart';

part 'call.g.dart';

@HiveType(typeId: 2)
class Call extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String callerId;

  @HiveField(2)
  List<String> participantIds;

  @HiveField(3)
  CallType type;

  @HiveField(4)
  CallStatus status;

  @HiveField(5)
  DateTime createdAt;

  @HiveField(6)
  DateTime? startedAt;

  @HiveField(7)
  DateTime? endedAt;

  @HiveField(8)
  int? duration; // in seconds

  @HiveField(9)
  String? roomId;

  @HiveField(10)
  bool isVideoEnabled;

  @HiveField(11)
  bool isAudioEnabled;

  @HiveField(12)
  CallEndReason? endReason;

  @HiveField(13)
  Map<String, dynamic>? metadata;

  Call({
    required this.id,
    required this.callerId,
    required this.participantIds,
    required this.type,
    this.status = CallStatus.pending,
    DateTime? createdAt,
    this.startedAt,
    this.endedAt,
    this.duration,
    this.roomId,
    this.isVideoEnabled = true,
    this.isAudioEnabled = true,
    this.endReason,
    this.metadata,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Create a copy of the call with updated fields
  Call copyWith({
    String? id,
    String? callerId,
    List<String>? participantIds,
    CallType? type,
    CallStatus? status,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? endedAt,
    int? duration,
    String? roomId,
    bool? isVideoEnabled,
    bool? isAudioEnabled,
    CallEndReason? endReason,
    Map<String, dynamic>? metadata,
  }) {
    return Call(
      id: id ?? this.id,
      callerId: callerId ?? this.callerId,
      participantIds: participantIds ?? this.participantIds,
      type: type ?? this.type,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      endedAt: endedAt ?? this.endedAt,
      duration: duration ?? this.duration,
      roomId: roomId ?? this.roomId,
      isVideoEnabled: isVideoEnabled ?? this.isVideoEnabled,
      isAudioEnabled: isAudioEnabled ?? this.isAudioEnabled,
      endReason: endReason ?? this.endReason,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert call to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'callerId': callerId,
      'participantIds': participantIds,
      'type': type.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'endedAt': endedAt?.toIso8601String(),
      'duration': duration,
      'roomId': roomId,
      'isVideoEnabled': isVideoEnabled,
      'isAudioEnabled': isAudioEnabled,
      'endReason': endReason?.name,
      'metadata': metadata,
    };
  }

  /// Create call from JSON
  factory Call.fromJson(Map<String, dynamic> json) {
    return Call(
      id: json['id'] as String,
      callerId: json['callerId'] as String,
      participantIds: List<String>.from(json['participantIds'] as List),
      type: CallType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CallType.voice,
      ),
      status: CallStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => CallStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      startedAt: json['startedAt'] != null
          ? DateTime.parse(json['startedAt'] as String)
          : null,
      endedAt: json['endedAt'] != null
          ? DateTime.parse(json['endedAt'] as String)
          : null,
      duration: json['duration'] as int?,
      roomId: json['roomId'] as String?,
      isVideoEnabled: json['isVideoEnabled'] as bool? ?? true,
      isAudioEnabled: json['isAudioEnabled'] as bool? ?? true,
      endReason: json['endReason'] != null
          ? CallEndReason.values.firstWhere(
              (e) => e.name == json['endReason'],
              orElse: () => CallEndReason.unknown,
            )
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Check if call is one-on-one
  bool get isOneOnOne => participantIds.length == 2;

  /// Check if call is group call
  bool get isGroupCall => participantIds.length > 2;

  /// Check if call is active
  bool get isActive => status == CallStatus.connected;

  /// Check if call is ended
  bool get isEnded => status == CallStatus.ended;

  /// Check if call is missed
  bool get isMissed => status == CallStatus.missed;

  /// Get call duration text
  String get durationText {
    if (duration == null) return '00:00';
    
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Get call status text
  String get statusText {
    switch (status) {
      case CallStatus.pending:
        return 'Calling...';
      case CallStatus.ringing:
        return 'Ringing...';
      case CallStatus.connected:
        return 'Connected';
      case CallStatus.ended:
        return 'Ended';
      case CallStatus.missed:
        return 'Missed';
      case CallStatus.declined:
        return 'Declined';
      case CallStatus.timeout:
        return 'No answer';
      case CallStatus.failed:
        return 'Failed';
    }
  }

  /// Get call type text
  String get typeText {
    switch (type) {
      case CallType.voice:
        return 'Voice Call';
      case CallType.video:
        return 'Video Call';
      case CallType.conference:
        return 'Conference';
    }
  }

  @override
  String toString() {
    return 'Call(id: $id, type: $type, status: $status, participants: ${participantIds.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Call && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@HiveType(typeId: 3)
enum CallType {
  @HiveField(0)
  voice,
  @HiveField(1)
  video,
  @HiveField(2)
  conference,
}

@HiveType(typeId: 4)
enum CallStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  ringing,
  @HiveField(2)
  connected,
  @HiveField(3)
  ended,
  @HiveField(4)
  missed,
  @HiveField(5)
  declined,
  @HiveField(6)
  timeout,
  @HiveField(7)
  failed,
}

@HiveType(typeId: 5)
enum CallEndReason {
  @HiveField(0)
  hangup,
  @HiveField(1)
  timeout,
  @HiveField(2)
  declined,
  @HiveField(3)
  networkError,
  @HiveField(4)
  unknown,
}
