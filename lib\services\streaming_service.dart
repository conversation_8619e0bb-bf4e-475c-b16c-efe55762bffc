import 'dart:async';
import 'package:uuid/uuid.dart';

import '../models/stream.dart';
import 'authentication_service.dart';

/// Service for handling live streaming functionality
class StreamingService {
  static final StreamingService _instance = StreamingService._internal();
  factory StreamingService() => _instance;
  StreamingService._internal();

  final StreamController<LiveStream> _streamController = StreamController<LiveStream>.broadcast();
  final StreamController<List<LiveStream>> _streamsListController = StreamController<List<LiveStream>>.broadcast();
  final StreamController<int> _viewerCountController = StreamController<int>.broadcast();
  
  final List<LiveStream> _activeStreams = [];
  LiveStream? _currentStream;

  /// Stream of stream state changes
  Stream<LiveStream> get streamStateStream => _streamController.stream;
  
  /// Stream of active streams list
  Stream<List<LiveStream>> get streamsListStream => _streamsListController.stream;
  
  /// Stream of viewer count changes
  Stream<int> get viewerCountStream => _viewerCountController.stream;
  
  /// Get current stream
  LiveStream? get currentStream => _currentStream;
  
  /// Get active streams
  List<LiveStream> get activeStreams => List.unmodifiable(_activeStreams);

  /// Initialize the streaming service
  Future<void> initialize() async {
    // Load demo streams
    _loadDemoStreams();
  }

  /// Start a live stream
  Future<LiveStream> startLiveStream({
    required String title,
    String? description,
    StreamQuality quality = StreamQuality.hd,
    bool isPrivate = false,
    String? password,
    List<String> tags = const [],
    String? category,
  }) async {
    final currentUser = AuthenticationService().currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final streamId = const Uuid().v4();
    final stream = LiveStream(
      id: streamId,
      hostId: currentUser.id,
      title: title,
      description: description,
      quality: quality,
      status: StreamStatus.live,
      startedAt: DateTime.now(),
      isPrivate: isPrivate,
      password: password,
      tags: tags,
      category: category,
    );

    _currentStream = stream;
    _activeStreams.insert(0, stream);
    
    _streamController.add(stream);
    _streamsListController.add(_activeStreams);

    // Start viewer count simulation
    _startViewerCountSimulation(streamId);

    return stream;
  }

  /// End current live stream
  Future<void> endLiveStream() async {
    if (_currentStream == null) return;

    final endedStream = _currentStream!.copyWith(
      status: StreamStatus.ended,
      endedAt: DateTime.now(),
      duration: _currentStream!.startedAt != null
          ? DateTime.now().difference(_currentStream!.startedAt!).inSeconds
          : 0,
    );

    // Update in active streams list
    final index = _activeStreams.indexWhere((s) => s.id == endedStream.id);
    if (index != -1) {
      _activeStreams[index] = endedStream;
    }

    _currentStream = null;
    _streamController.add(endedStream);
    _streamsListController.add(_activeStreams);
  }

  /// Join a live stream as viewer
  Future<void> joinStream(String streamId) async {
    final stream = _activeStreams.firstWhere(
      (s) => s.id == streamId,
      orElse: () => throw Exception('Stream not found'),
    );

    if (stream.status != StreamStatus.live) {
      throw Exception('Stream is not live');
    }

    // Increment viewer count
    final updatedStream = stream.copyWith(
      viewerCount: stream.viewerCount + 1,
      maxViewers: stream.viewerCount + 1 > stream.maxViewers 
          ? stream.viewerCount + 1 
          : stream.maxViewers,
    );

    _updateStreamInList(updatedStream);
  }

  /// Leave a live stream
  Future<void> leaveStream(String streamId) async {
    final stream = _activeStreams.firstWhere(
      (s) => s.id == streamId,
      orElse: () => throw Exception('Stream not found'),
    );

    // Decrement viewer count
    final updatedStream = stream.copyWith(
      viewerCount: (stream.viewerCount - 1).clamp(0, double.infinity).toInt(),
    );

    _updateStreamInList(updatedStream);
  }



  /// Search streams by title or tags
  List<LiveStream> searchStreams(String query) {
    if (query.isEmpty) return _activeStreams;

    final lowerQuery = query.toLowerCase();
    return _activeStreams.where((stream) {
      return stream.title.toLowerCase().contains(lowerQuery) ||
             stream.description?.toLowerCase().contains(lowerQuery) == true ||
             stream.tags.any((tag) => tag.toLowerCase().contains(lowerQuery)) ||
             stream.category?.toLowerCase().contains(lowerQuery) == true;
    }).toList();
  }

  /// Get streams by category
  List<LiveStream> getStreamsByCategory(String category) {
    return _activeStreams.where((stream) => stream.category == category).toList();
  }

  /// Get popular streams (by viewer count)
  List<LiveStream> getPopularStreams() {
    final streams = List<LiveStream>.from(_activeStreams);
    streams.sort((a, b) => b.viewerCount.compareTo(a.viewerCount));
    return streams;
  }

  /// Update stream in list
  void _updateStreamInList(LiveStream updatedStream) {
    final index = _activeStreams.indexWhere((s) => s.id == updatedStream.id);
    if (index != -1) {
      _activeStreams[index] = updatedStream;
      _streamController.add(updatedStream);
      _streamsListController.add(_activeStreams);
    }
  }

  /// Start viewer count simulation for demo
  void _startViewerCountSimulation(String streamId) {
    Timer.periodic(const Duration(seconds: 10), (timer) {
      final stream = _activeStreams.firstWhere(
        (s) => s.id == streamId,
        orElse: () => LiveStream(id: '', hostId: '', title: ''),
      );

      if (stream.id.isEmpty || stream.status != StreamStatus.live) {
        timer.cancel();
        return;
      }

      // Simulate viewer count changes
      final change = (DateTime.now().millisecond % 3) - 1; // -1, 0, or 1
      final newCount = (stream.viewerCount + change).clamp(0, 1000);
      
      final updatedStream = stream.copyWith(
        viewerCount: newCount,
        maxViewers: newCount > stream.maxViewers ? newCount : stream.maxViewers,
      );

      _updateStreamInList(updatedStream);
      _viewerCountController.add(newCount);
    });
  }

  /// Load demo streams for testing
  void _loadDemoStreams() {
    final demoStreams = [
      LiveStream(
        id: 'demo_1',
        hostId: 'alice_001',
        title: 'Flutter Development Live',
        description: 'Building amazing apps with Flutter',
        status: StreamStatus.live,
        startedAt: DateTime.now().subtract(const Duration(minutes: 30)),
        viewerCount: 156,
        maxViewers: 200,
        tags: ['flutter', 'development', 'coding'],
        category: 'Technology',
        allowComments: true,
        allowReactions: true,
      ),
      LiveStream(
        id: 'demo_2',
        hostId: 'bob_002',
        title: 'Music Session',
        description: 'Live acoustic guitar performance',
        status: StreamStatus.live,
        startedAt: DateTime.now().subtract(const Duration(minutes: 15)),
        viewerCount: 89,
        maxViewers: 120,
        tags: ['music', 'guitar', 'acoustic'],
        category: 'Music',
        allowComments: true,
        allowReactions: true,
      ),
      LiveStream(
        id: 'demo_3',
        hostId: 'charlie_003',
        title: 'Gaming Stream',
        description: 'Playing the latest indie games',
        status: StreamStatus.live,
        startedAt: DateTime.now().subtract(const Duration(hours: 1)),
        viewerCount: 234,
        maxViewers: 300,
        tags: ['gaming', 'indie', 'entertainment'],
        category: 'Gaming',
        allowComments: true,
        allowReactions: true,
      ),
    ];

    _activeStreams.addAll(demoStreams);
    _streamsListController.add(_activeStreams);

    // Start viewer count simulation for demo streams
    for (final stream in demoStreams) {
      _startViewerCountSimulation(stream.id);
    }
  }

  /// Dispose the service
  void dispose() {
    _streamController.close();
    _streamsListController.close();
    _viewerCountController.close();
  }
}
