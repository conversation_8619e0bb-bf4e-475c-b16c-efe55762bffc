import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/call.dart';
import '../../models/user.dart';
import '../../services/call_service.dart';
import '../../services/authentication_service.dart';
import '../../widgets/user_selection_dialog.dart';

/// Screen showing call history
class CallHistoryScreen extends StatefulWidget {
  const CallHistoryScreen({super.key});

  @override
  State<CallHistoryScreen> createState() => _CallHistoryScreenState();
}

class _CallHistoryScreenState extends State<CallHistoryScreen> {
  final CallService _callService = CallService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Call History'),
        actions: [
          IconButton(
            icon: const Icon(Icons.call),
            onPressed: _showCallOptions,
          ),
        ],
      ),
      body: StreamBuilder<List<Call>>(
        stream: _callService.callHistoryStream,
        initialData: _callService.callHistory,
        builder: (context, snapshot) {
          final calls = snapshot.data ?? [];

          if (calls.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.call_end,
                    size: 80,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No call history',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Your call history will appear here',
                    style: TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: calls.length,
            itemBuilder: (context, index) {
              final call = calls[index];
              return _buildCallHistoryItem(call);
            },
          );
        },
      ),
    );
  }

  Widget _buildCallHistoryItem(Call call) {
    final currentUser = AuthenticationService().currentUser;
    final isOutgoing = call.callerId == currentUser?.id;
    final otherParticipants = call.participantIds
        .where((id) => id != currentUser?.id)
        .toList();

    // For demo purposes, create user names from IDs
    final participantNames = otherParticipants.map((id) {
      // Try to find the user in test users
      final testUser = TestUser.values.where((tu) => tu.id == id).firstOrNull;
      return testUser?.name ?? 'User ${id.substring(0, 8)}';
    }).join(', ');

    final callTypeIcon = _getCallTypeIcon(call.type);
    final callStatusIcon = _getCallStatusIcon(call.status, isOutgoing);
    final duration = _formatDuration(call.duration);

    return ListTile(
      leading: Stack(
        children: [
          CircleAvatar(
            child: Icon(callTypeIcon),
          ),
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: Icon(
                callStatusIcon,
                size: 12,
                color: _getCallStatusColor(call.status, isOutgoing),
              ),
            ),
          ),
        ],
      ),
      title: Text(
        participantNames.isNotEmpty ? participantNames : 'Unknown',
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${isOutgoing ? 'Outgoing' : 'Incoming'} ${_getCallTypeText(call.type)}',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          if (call.startedAt != null)
            Text(
              _formatDateTime(call.startedAt!),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 11,
              ),
            ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (duration.isNotEmpty)
            Text(
              duration,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          const SizedBox(height: 4),
          IconButton(
            icon: Icon(
              call.type == CallType.video ? Icons.videocam : Icons.call,
              size: 20,
            ),
            onPressed: () => _redialCall(call),
          ),
        ],
      ),
      onTap: () => _showCallDetails(call),
    );
  }

  IconData _getCallTypeIcon(CallType type) {
    switch (type) {
      case CallType.voice:
        return Icons.call;
      case CallType.video:
        return Icons.videocam;
      case CallType.conference:
        return Icons.group;
    }
  }

  IconData _getCallStatusIcon(CallStatus status, bool isOutgoing) {
    switch (status) {
      case CallStatus.pending:
      case CallStatus.ringing:
        return isOutgoing ? Icons.call_made : Icons.call_received;
      case CallStatus.connected:
      case CallStatus.ended:
        return isOutgoing ? Icons.call_made : Icons.call_received;
      case CallStatus.missed:
        return Icons.call_received;
      case CallStatus.declined:
        return Icons.call_end;
      case CallStatus.timeout:
        return Icons.timer_off;
      case CallStatus.failed:
        return Icons.error_outline;
    }
  }

  Color _getCallStatusColor(CallStatus status, bool isOutgoing) {
    switch (status) {
      case CallStatus.connected:
      case CallStatus.ended:
        return Colors.green;
      case CallStatus.missed:
        return Colors.red;
      case CallStatus.declined:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getCallTypeText(CallType type) {
    switch (type) {
      case CallType.voice:
        return 'voice call';
      case CallType.video:
        return 'video call';
      case CallType.conference:
        return 'group call';
    }
  }

  String _formatDuration(int? seconds) {
    if (seconds == null || seconds == 0) return '';
    
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return '${minutes}m ${remainingSeconds}s';
    } else {
      return '${remainingSeconds}s';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      // Today
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      // This week
      final weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return weekdays[dateTime.weekday - 1];
    } else {
      // Older
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  void _showCallOptions() async {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.call),
              title: const Text('Voice Call'),
              onTap: () {
                Navigator.pop(context);
                _initiateCall(false);
              },
            ),
            ListTile(
              leading: const Icon(Icons.videocam),
              title: const Text('Video Call'),
              onTap: () {
                Navigator.pop(context);
                _initiateCall(true);
              },
            ),
            ListTile(
              leading: const Icon(Icons.group),
              title: const Text('Group Call'),
              onTap: () {
                Navigator.pop(context);
                _initiateGroupCall();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _initiateCall(bool isVideo) async {
    final users = await showUserSelectionDialog(
      context: context,
      title: 'Select user to call',
      allowMultipleSelection: false,
    );

    if (users != null && users.isNotEmpty) {
      final user = users.first;
      try {
        if (isVideo) {
          await _callService.makeVideoCall(
            targetUserId: user.id,
            targetUserName: user.name,
            targetUserAvatar: user.avatarUrl,
          );
        } else {
          await _callService.makeVoiceCall(
            targetUserId: user.id,
            targetUserName: user.name,
            targetUserAvatar: user.avatarUrl,
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to start call: $e')),
          );
        }
      }
    }
  }

  void _initiateGroupCall() async {
    final users = await showUserSelectionDialog(
      context: context,
      title: 'Select users for group call',
      allowMultipleSelection: true,
    );

    if (users != null && users.isNotEmpty) {
      try {
        await _callService.makeGroupCall(
          participants: users,
          isVideoCall: true,
        );
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to start group call: $e')),
          );
        }
      }
    }
  }

  void _redialCall(Call call) {
    // Implementation for redialing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Redial functionality coming soon')),
    );
  }

  void _showCallDetails(Call call) {
    // Implementation for showing call details
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Call details coming soon')),
    );
  }
}
